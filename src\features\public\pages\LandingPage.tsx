import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import '../styles/landing-page.css';
import logoImage from '@/assets/image1.png';
import {
  Building2,
  Shield,
  Truck,
  CheckCircle,
  ArrowRight,
  Phone,
  Mail,
  TrendingUp,
  DollarSign,
  Clock,
  BarChart3,

  Wrench,
  Users2,
  ChevronRight,
  Quote,
  User,
  Menu
} from 'lucide-react';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();



  // Key metrics and value propositions
  const keyMetrics = [
    { value: "40%", label: "Faster Repair Turnaround", icon: <Clock className="h-6 w-6" /> },
    { value: "100%", label: "Compliance Tracking", icon: <Shield className="h-6 w-6" /> },
    { value: "35%", label: "Cost Reduction", icon: <DollarSign className="h-6 w-6" /> },
    { value: "7", label: "Integrated Modules", icon: <BarChart3 className="h-6 w-6" /> }
  ];

  // Primary features with business impact
  const primaryFeatures = [
    {
      icon: <TrendingUp className="h-12 w-12 text-blue-600" />,
      title: "Intelligent Work Allocation",
      description: "Rules-based engine ensures fair, auditable work distribution with 40% faster turnaround times through optimal merchant matching",
      benefits: ["Automated merchant selection", "Capacity-based allocation", "Performance tracking", "Location optimization"],
      roi: "40% faster repairs"
    },
    {
      icon: <Shield className="h-12 w-12 text-green-600" />,
      title: "Compliance & Inspection Management",
      description: "Real-time compliance monitoring with mobile inspection apps, ensuring 100% regulatory adherence and audit readiness",
      benefits: ["Mobile inspection reports", "Automated compliance tracking", "Merchant accreditation", "Audit trail management"],
      roi: "Zero compliance violations"
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-purple-600" />,
      title: "Cost Control & Analytics",
      description: "Benchmark parts and labor costs automatically, with anomaly detection saving 25-35% on maintenance expenses",
      benefits: ["Automated cost benchmarking", "Invoice verification", "Anomaly detection", "Performance dashboards"],
      roi: "35% cost reduction"
    }
  ];

  // Core system modules
  const coreModules = [
    {
      icon: <Truck className="h-6 w-6 text-blue-600" />,
      title: "Vehicle & Asset Management",
      description: "Complete tracking of government vehicles, status, service history, insurance, and documentation with real-time updates"
    },
    {
      icon: <Users2 className="h-6 w-6 text-orange-600" />,
      title: "Merchant Network Management",
      description: "Onboard, accredit, and monitor workshops with compliance checks, inspection results, and real-time staff/equipment tracking"
    },
    {
      icon: <BarChart3 className="h-6 w-6 text-green-600" />,
      title: "Work Allocation Engine",
      description: "Rules-based engine for fair, auditable work distribution considering capacity, specialization, location, and performance"
    },
    {
      icon: <Wrench className="h-6 w-6 text-purple-600" />,
      title: "Maintenance & Repair Scheduling",
      description: "Automate preventive maintenance, generate work orders, and track progress with telematics integration"
    },
    {
      icon: <DollarSign className="h-6 w-6 text-indigo-600" />,
      title: "Billing & Cost Control",
      description: "Benchmark parts and labor costs, automate invoice verification, and flag anomalies for immediate review"
    },
    {
      icon: <Shield className="h-6 w-6 text-red-600" />,
      title: "Inspection & Compliance",
      description: "Schedule and record inspections with mobile apps for real-time compliance tracking and reporting"
    }
  ];

  // Client testimonials
  const testimonials = [
    {
      quote: "The work allocation engine has revolutionized our repair process. Fair distribution and 40% faster turnaround times have eliminated our maintenance backlog.",
      author: "Sarah Mthembu",
      position: "Fleet Manager",
      department: "Department of Transport - Gauteng",
      metrics: "40% faster repairs, Zero backlog"
    },
    {
      quote: "Real-time compliance monitoring and mobile inspection apps have given us complete visibility. We haven't had a single compliance violation since implementation.",
      author: "David Nkomo",
      position: "Operations Director",
      department: "City of Cape Town",
      metrics: "100% compliance, Real-time visibility"
    },
    {
      quote: "The automated cost benchmarking caught R2.3M in billing anomalies in our first year. The system has already paid for itself multiple times over.",
      author: "Nomsa Dlamini",
      position: "CFO",
      department: "KwaZulu-Natal Provincial Government",
      metrics: "R2.3M savings identified"
    }
  ];

  // Trust indicators
  const certifications = [
    "ISO 27001 Certified",
    "POPIA Compliant",
    "Government Approved",
    "SOC 2 Type II"
  ];

  const benefits = [
    "Rules-based work allocation engine for fair, auditable distribution",
    "Automated merchant accreditation and compliance monitoring",
    "Real-time cost benchmarking with anomaly detection",
    "Mobile inspection apps for instant compliance reporting",
    "Integrated telematics for predictive maintenance scheduling",
    "Comprehensive analytics dashboards for all stakeholders",
    "Invoice verification and automated billing controls",
    "Government-grade security with full audit trails"
  ];

  return (
    <div className="min-h-screen bg-slate-50" style={{ fontFamily: "'IBM Plex Sans', sans-serif" }}>
      {/* Navigation */}
      <nav className="relative z-10 px-6 py-6 flex justify-between items-center">
          <div className="flex items-center">
            <img src={logoImage} alt="RT46-2026 Fleet Management" className="h-8 w-auto" />
            <span className="ml-2 text-xl font-bold text-slate-800">RT46-2026 Fleet Management</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-slate-600 hover:text-blue-600 transition">Features</a>
            <a href="#pricing" className="text-slate-600 hover:text-blue-600 transition">ROI Calculator</a>
            <a href="#testimonials" className="text-slate-600 hover:text-blue-600 transition">Success Stories</a>
            <a href="#modules" className="text-slate-600 hover:text-blue-600 transition">Modules</a>
          </div>
          <div className="hidden md:flex">
            <button
              onClick={() => navigate('/login')}
              className="px-5 py-2 text-blue-600 hover:text-blue-700 font-medium"
            >
              Login
            </button>
            <button
              onClick={() => navigate('/onboard')}
              className="px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
            >
              Request Demo
            </button>
          </div>
          <button className="md:hidden">
            <Menu className="w-6 h-6 text-slate-600" />
          </button>
        </nav>

        {/* Hero Section */}
        <div className="relative px-6 lg:px-16 py-12 md:py-20 max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8 z-10">
              <div>
                <span className="px-3 py-1 text-sm text-blue-600 bg-blue-50 bg-opacity-80 rounded-full font-medium">
                  Government-Approved Platform
                </span>
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold text-slate-900 leading-tight">
                Intelligent Work Allocation <span className="text-gradient">Cuts Repair Time</span> by 40%
              </h1>
              <p className="text-xl text-slate-600 max-w-lg">
                The only government-approved fleet management platform with rules-based work allocation,
                automated compliance monitoring, and real-time cost control for South African institutions.
              </p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <button
                  onClick={() => navigate('/onboard')}
                  className="px-8 py-4 text-white btn-primary rounded-lg font-medium text-center"
                >
                  Get Your ROI Calculator
                </button>
                <button
                  className="px-8 py-4 text-blue-600 bg-white border border-blue-200 rounded-lg font-medium text-center hover:border-blue-300 transition"
                >
                  Watch 3-Min Demo
                </button>
              </div>

              {/* Features list */}
              <div className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 mt-1 text-green-500" />
                    <span className="text-slate-700">Rules-Based Allocation</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 mt-1 text-green-500" />
                    <span className="text-slate-700">Real-Time Compliance</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 mt-1 text-green-500" />
                    <span className="text-slate-700">Cost Benchmarking</span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 mt-1 text-green-500" />
                    <span className="text-slate-700">Mobile Inspections</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Image */}
            <div className="relative z-10">
              <div className="relative floating">
                <div className="bg-gradient-to-br from-blue-50 to-white p-8 rounded-xl shadow-2xl">
                  <div className="space-y-6">
                    {/* Key metrics display */}
                    <div className="grid grid-cols-2 gap-4">
                      {keyMetrics.map((metric, index) => (
                        <div key={index} className="text-center p-4 bg-white rounded-lg shadow-sm">
                          <div className="flex justify-center mb-2 text-blue-600">
                            <div className="w-6 h-6">
                              {metric.icon}
                            </div>
                          </div>
                          <div className="text-2xl font-bold text-slate-900">{metric.value}</div>
                          <div className="text-xs text-slate-600">{metric.label}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Floating UI elements */}
                <div className="absolute -top-6 -right-6 bg-white rounded-lg shadow-lg p-4 z-20">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">Real-time Monitoring</span>
                  </div>
                </div>

                <div className="absolute -bottom-6 -left-6 bg-white rounded-lg shadow-lg p-4 z-20">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-blue-500" />
                    <span className="text-sm font-medium">40% Faster Repairs</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social proof */}
          <div className="mt-16 border-t border-slate-200 pt-8 z-10 relative">
            <p className="text-slate-500 text-center mb-6">Trusted by government departments across South Africa</p>
            <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
              {certifications.map((cert, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="bg-blue-50 text-blue-600 border-blue-200 text-xs sm:text-sm px-3 py-1"
                >
                  {cert}
                </Badge>
              ))}
            </div>
          </div>
        </div>



      {/* Primary Value Propositions */}
      <section id="features" className="py-12 sm:py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-3 sm:mb-4 px-4">
              Proven Results for Government Fleets
            </h2>
            <p className="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto px-4">
              Join 500+ government vehicles already saving costs and improving efficiency with RT46-2026
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {primaryFeatures.map((feature, index) => (
              <Card key={index} className="relative overflow-hidden card-hover border-0 shadow-lg bg-white">
                <div className="absolute top-0 right-0 bg-gradient-to-l from-blue-600 to-transparent w-24 h-24 sm:w-32 sm:h-32 opacity-10"></div>
                <CardHeader className="pb-3 sm:pb-4 p-4 sm:p-6">
                  <div className="mb-3 sm:mb-4">
                    <div className="w-10 h-10 sm:w-12 sm:h-12">
                      {feature.icon}
                    </div>
                  </div>
                  <CardTitle className="text-xl sm:text-2xl font-bold text-slate-900 mb-2 leading-tight">
                    {feature.title}
                  </CardTitle>
                  <Badge className="bg-green-100 text-green-800 w-fit text-xs sm:text-sm">
                    {feature.roi}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6 pt-0">
                  <p className="text-slate-600 text-base sm:text-lg leading-relaxed">
                    {feature.description}
                  </p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start gap-2 text-slate-700">
                        <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                        <span className="text-sm sm:text-base">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    variant="outline"
                    className="w-full mt-3 sm:mt-4 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white h-10 sm:h-11 transition-all duration-300"
                    onClick={() => navigate('/onboard')}
                  >
                    Learn More
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof - Testimonials */}
      <section id="testimonials" className="py-12 sm:py-16 lg:py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-3 sm:mb-4 px-4">
              Trusted by Government Leaders
            </h2>
            <p className="text-lg sm:text-xl text-slate-600 px-4">
              See how RT46-2026 is transforming fleet operations across South Africa
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="relative border-0 shadow-lg card-hover bg-white">
                <Quote className="absolute top-3 right-3 sm:top-4 sm:right-4 h-6 w-6 sm:h-8 sm:w-8 text-blue-200" />
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <p className="text-slate-700 mb-4 sm:mb-6 italic text-base sm:text-lg leading-relaxed pr-8">
                    "{testimonial.quote}"
                  </p>
                  <div className="border-t border-slate-200 pt-4 sm:pt-6">
                    <div className="flex items-center gap-3 sm:gap-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-slate-900 text-sm sm:text-base truncate">{testimonial.author}</p>
                        <p className="text-xs sm:text-sm text-slate-600 truncate">{testimonial.position}</p>
                        <p className="text-xs sm:text-sm text-blue-600 truncate">{testimonial.department}</p>
                      </div>
                    </div>
                    <Badge className="mt-3 sm:mt-4 bg-green-100 text-green-800 text-xs sm:text-sm">
                      {testimonial.metrics}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Core Modules Grid */}
      <section id="modules" className="py-12 sm:py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-3 sm:mb-4 px-4">
              7 Integrated Modules for Complete Fleet Control
            </h2>
            <p className="text-lg sm:text-xl text-slate-600 max-w-3xl mx-auto px-4">
              Purpose-built modules that work together seamlessly to deliver comprehensive fleet management and regulatory compliance
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {coreModules.map((module, index) => (
              <Card key={index} className="card-hover border-0 shadow-md bg-white">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-3 sm:mb-4">
                    <div className="p-2 sm:p-3 bg-slate-50 rounded-lg flex-shrink-0">
                      <div className="w-5 h-5 sm:w-6 sm:h-6">
                        {module.icon}
                      </div>
                    </div>
                    <h3 className="text-base sm:text-lg font-semibold text-slate-900 leading-tight">{module.title}</h3>
                  </div>
                  <p className="text-sm sm:text-base text-slate-600 leading-relaxed">{module.description}</p>
                </CardContent>
              </Card>
            ))}

            {/* Reporting & Analytics Module */}
            <Card className="card-hover border-0 shadow-md bg-white">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <div className="p-2 sm:p-3 bg-slate-50 rounded-lg flex-shrink-0">
                    <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-teal-600" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-slate-900 leading-tight">Reporting & Analytics</h3>
                </div>
                <p className="text-sm sm:text-base text-slate-600 leading-relaxed">Comprehensive dashboards showing turnaround times, downtime, merchant performance, compliance breaches, and cost trends</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* ROI Calculator CTA */}
      <section id="pricing" className="py-12 sm:py-16 lg:py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 px-4">
              Quantify Your Work Allocation Efficiency Gains
            </h2>
            <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 leading-relaxed px-4">
              Calculate how much your department can save with intelligent work allocation,
              automated compliance monitoring, and real-time cost control.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 mx-2 sm:mx-0 border border-white/20">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 text-center">
                <div className="p-3 sm:p-4">
                  <div className="text-2xl sm:text-3xl font-bold text-yellow-400">40%</div>
                  <div className="text-sm sm:text-base text-blue-100 mt-1">Faster Repair Turnaround</div>
                </div>
                <div className="p-3 sm:p-4">
                  <div className="text-2xl sm:text-3xl font-bold text-yellow-400">R2.3M+</div>
                  <div className="text-sm sm:text-base text-blue-100 mt-1">Billing Anomalies Detected</div>
                </div>
                <div className="p-3 sm:p-4">
                  <div className="text-2xl sm:text-3xl font-bold text-yellow-400">100%</div>
                  <div className="text-sm sm:text-base text-blue-100 mt-1">Compliance Achievement</div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4">
              <button
                onClick={() => navigate('/onboard')}
                className="px-8 py-4 bg-yellow-500 hover:bg-yellow-600 text-black text-base sm:text-lg font-semibold rounded-lg btn-primary"
              >
                <span className="hidden sm:inline">Get Your Free ROI Report</span>
                <span className="sm:hidden">Get ROI Report</span>
                <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 inline" />
              </button>
              <button className="px-8 py-4 border border-white text-white hover:bg-white hover:text-blue-800 text-base sm:text-lg rounded-lg transition-all duration-300">
                <span className="hidden sm:inline">Schedule Consultation</span>
                <span className="sm:hidden">Schedule Call</span>
              </button>
            </div>

            <p className="text-xs sm:text-sm text-blue-200 mt-4 sm:mt-6 px-4">
              ✓ Personalized savings estimate ✓ Implementation roadmap ✓ No obligation
            </p>
          </div>
        </div>
      </section>

      {/* Benefits Section with Visual */}
      <section className="py-12 sm:py-16 lg:py-20 bg-slate-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-start lg:items-center">
            <div className="order-2 lg:order-1">
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-900 mb-4 sm:mb-6">
                7 Integrated Modules, One Powerful Platform
              </h2>
              <p className="text-lg sm:text-xl text-slate-600 mb-6 sm:mb-8 leading-relaxed">
                From intelligent work allocation to real-time compliance monitoring, RT46-2026
                delivers comprehensive fleet management with measurable results for government institutions.
              </p>
              <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-slate-700 text-base sm:text-lg leading-relaxed">{benefit}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-blue-50 rounded-lg border-l-4 border-blue-600">
                <h4 className="font-semibold text-slate-900 mb-2 text-base sm:text-lg">Intelligent Work Allocation Guarantee</h4>
                <p className="text-slate-700 text-sm sm:text-base leading-relaxed">
                  Our rules-based allocation engine ensures fair, auditable work distribution with
                  complete transparency. Real-time compliance monitoring and mobile inspection apps
                  guarantee 100% regulatory adherence with full audit trails.
                </p>
              </div>
            </div>

            <div className="order-1 lg:order-2 bg-gradient-to-br from-blue-50 to-white p-6 sm:p-8 rounded-lg shadow-lg border border-slate-200">
              <h3 className="text-xl sm:text-2xl font-bold text-slate-900 mb-4 sm:mb-6">
                Start Your Digital Transformation
              </h3>
              <p className="text-slate-600 mb-4 sm:mb-6 text-sm sm:text-base">
                Join the growing number of government departments already benefiting from
                RT46-2026. Get started with a free pilot program.
              </p>

              <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">1</div>
                  <span className="text-slate-700 text-sm sm:text-base">Submit application (10 minutes)</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">2</div>
                  <span className="text-slate-700 text-sm sm:text-base">Free consultation & ROI assessment</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-7 h-7 sm:w-8 sm:h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0">3</div>
                  <span className="text-slate-700 text-sm sm:text-base">30-day pilot program launch</span>
                </div>
              </div>

              <button
                onClick={() => navigate('/onboard')}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white text-base sm:text-lg py-3 sm:py-4 rounded-lg btn-primary"
              >
                <span className="hidden sm:inline">Start Free Pilot Program</span>
                <span className="sm:hidden">Start Free Pilot</span>
                <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 inline" />
              </button>
              <p className="text-xs sm:text-sm text-slate-500 mt-3 sm:mt-4 text-center">
                No setup fees • 30-day free trial • Cancel anytime
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20 bg-slate-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 px-4">
              Ready to Transform Your Fleet Operations?
            </h2>
            <p className="text-lg sm:text-xl text-slate-300 mb-6 sm:mb-8 leading-relaxed px-4">
              Join hundreds of government departments already saving costs and improving efficiency.
              Start your journey to smarter fleet management today.
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-8 sm:mb-12">
              <div className="text-center p-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                  <Phone className="h-6 w-6 sm:h-8 sm:w-8" />
                </div>
                <h3 className="text-base sm:text-lg font-semibold mb-2">Expert Consultation</h3>
                <p className="text-slate-400 text-sm sm:text-base mb-2">Speak with our fleet management specialists</p>
                <p className="text-blue-400 text-sm sm:text-base">+27 12 345 6789</p>
              </div>
              <div className="text-center p-4">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                  <Mail className="h-6 w-6 sm:h-8 sm:w-8" />
                </div>
                <h3 className="text-base sm:text-lg font-semibold mb-2">Email Support</h3>
                <p className="text-slate-400 text-sm sm:text-base mb-2">Get detailed information and documentation</p>
                <p className="text-blue-400 text-sm sm:text-base break-all"><EMAIL></p>
              </div>
              <div className="text-center p-4 sm:col-span-2 lg:col-span-1">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                  <Building2 className="h-6 w-6 sm:h-8 sm:w-8" />
                </div>
                <h3 className="text-base sm:text-lg font-semibold mb-2">Government Portal</h3>
                <p className="text-slate-400 text-sm sm:text-base mb-2">Access official documentation and resources</p>
                <p className="text-blue-400 text-sm sm:text-base">gov.za/rt46-fleet</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4">
              <button
                onClick={() => navigate('/onboard')}
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white text-base sm:text-lg rounded-lg btn-primary"
              >
                <span className="hidden sm:inline">Apply for Access Now</span>
                <span className="sm:hidden">Apply Now</span>
                <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 inline" />
              </button>
              <button
                onClick={() => navigate('/login')}
                className="px-8 py-4 border border-slate-600 text-slate-300 hover:bg-slate-800 text-base sm:text-lg rounded-lg transition-all duration-300"
              >
                <span className="hidden sm:inline">Existing User Login</span>
                <span className="sm:hidden">Login</span>
              </button>
            </div>

            <p className="text-xs sm:text-sm text-slate-400 mt-4 sm:mt-6 px-4">
              Trusted by 500+ government vehicles • ISO 27001 Certified • POPIA Compliant
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-8 sm:py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8">
            <div className="col-span-1 sm:col-span-2">
              <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                <Building2 className="h-6 w-6 sm:h-8 sm:w-8 text-blue-400" />
                <div>
                  <h3 className="text-lg sm:text-xl font-bold">RT46-2026</h3>
                  <p className="text-slate-400 text-xs sm:text-sm">Fleet Management System</p>
                </div>
              </div>
              <p className="text-slate-400 mb-3 sm:mb-4 max-w-md text-sm sm:text-base">
                The official fleet management platform for South African government institutions.
                Delivering measurable ROI while ensuring complete regulatory compliance.
              </p>
              <div className="flex flex-wrap gap-2 sm:gap-4">
                {certifications.map((cert, index) => (
                  <Badge key={index} variant="secondary" className="bg-slate-800 text-slate-300 text-xs">
                    {cert}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3 sm:mb-4 text-sm sm:text-base">Quick Links</h4>
              <ul className="space-y-1 sm:space-y-2 text-slate-400">
                <li><button onClick={() => navigate('/onboard')} className="hover:text-white transition-colors text-sm sm:text-base text-left">Apply Now</button></li>
                <li><button onClick={() => navigate('/login')} className="hover:text-white transition-colors text-sm sm:text-base text-left">Sign In</button></li>
                <li><a href="#features" className="hover:text-white transition-colors text-sm sm:text-base">Features</a></li>
                <li><a href="#pricing" className="hover:text-white transition-colors text-sm sm:text-base">ROI Calculator</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-3 sm:mb-4 text-sm sm:text-base">Support</h4>
              <ul className="space-y-1 sm:space-y-2 text-slate-400">
                <li><a href="tel:+27123456789" className="hover:text-white transition-colors text-sm sm:text-base">+27 12 345 6789</a></li>
                <li><a href="mailto:<EMAIL>" className="hover:text-white transition-colors text-sm sm:text-base break-all"><EMAIL></a></li>
                <li><a href="#" className="hover:text-white transition-colors text-sm sm:text-base">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors text-sm sm:text-base">Training</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 pt-6 sm:pt-8 text-center">
            <p className="text-slate-400 text-xs sm:text-sm leading-relaxed">
              © 2025 South African Government. All rights reserved.
              <br className="sm:hidden" />
              <span className="hidden sm:inline"> | </span>
              <a href="#" className="hover:text-white ml-1 text-sm sm:text-base">Privacy Policy</a> |
              <a href="#" className="hover:text-white ml-1 text-sm sm:text-base">Terms of Service</a> |
              <a href="#" className="hover:text-white ml-1 text-sm sm:text-base">POPIA Compliance</a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;

